<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
    >
      <template #right>
        <view class="nav-right flex items-center">
          <text
            class="i-carbon-settings text-40rpx text-secondary mr-20rpx"
            @tap="goToSettings"
          ></text>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 用户信息区域 -->
    <view class="user-section">
      <!-- 背景装饰 -->
      <view class="user-bg">
        <view class="bg-gradient"></view>
        <view class="bg-circles">
          <view class="circle circle-1"></view>
          <view class="circle circle-2"></view>
        </view>
      </view>

      <!-- 用户信息卡片 -->
      <view class="user-card">
        <!-- 未登录状态 -->
        <view v-if="!userStore.user" class="login-section" @tap="handleLogin">
          <view class="avatar-placeholder">
            <text class="i-carbon-user text-80rpx text-grey"></text>
          </view>
          <text class="login-text">点击登录</text>
          <text class="login-desc">登录后享受更多服务</text>
        </view>

        <!-- 已登录状态 -->
        <view v-else class="user-info-section">
          <view class="user-avatar-wrapper" @tap="handleAvatarTap">
            <image
              :src="userStore.user.avatar || getDefaultAvatar()"
              class="user-avatar"
              mode="aspectFill"
            />
            <view class="avatar-edit-icon">
              <text class="i-carbon-edit text-24rpx text-white"></text>
            </view>
          </view>

          <view class="user-details">
            <view class="user-name-section">
              <text class="user-name">{{ userStore.user.name || "用户" }}</text>
              <view v-if="userStore.user.isVip" class="vip-badge">
                <text class="i-carbon-star-filled text-24rpx"></text>
                <text class="vip-text">VIP</text>
              </view>
            </view>

            <text class="user-intro">{{
              userStore.user.intro || "这个人很懒，什么都没写~"
            }}</text>

            <view class="user-stats">
              <view class="stat-item" @tap="goToPage('/pages/user/followers')">
                <text class="stat-number">{{
                  userStore.user.followers || 0
                }}</text>
                <text class="stat-label">关注者</text>
              </view>
              <view class="stat-divider"></view>
              <view class="stat-item" @tap="goToPage('/pages/user/following')">
                <text class="stat-number">{{
                  userStore.user.following || 0
                }}</text>
                <text class="stat-label">关注</text>
              </view>
              <view class="stat-divider"></view>
              <view class="stat-item" @tap="goToPage('/pages/user/posts')">
                <text class="stat-number">{{ userStore.user.posts || 0 }}</text>
                <text class="stat-label">动态</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷功能区域 -->
    <view class="quick-actions" v-if="userStore.user">
      <view class="action-grid">
        <view
          v-for="(action, index) in quickActions"
          :key="index"
          class="action-item"
          @tap="goToPage(action.path)"
        >
          <view
            class="action-icon-wrapper"
            :style="{ backgroundColor: action.bgColor }"
          >
            <text :class="action.icon" class="action-icon"></text>
          </view>
          <text class="action-text">{{ action.title }}</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-section">
      <view
        v-for="(group, groupIndex) in menuGroups"
        :key="groupIndex"
        class="menu-group"
      >
        <view
          v-for="(item, index) in group"
          :key="index"
          class="menu-item"
          @tap="handleMenuClick(item)"
        >
          <view class="menu-left">
            <view class="menu-icon-wrapper">
              <text :class="item.icon" class="menu-icon"></text>
            </view>
            <text class="menu-title">{{ item.title }}</text>
          </view>

          <view class="menu-right">
            <text v-if="item.badge" class="menu-badge">{{ item.badge }}</text>
            <text class="i-carbon-chevron-right text-28rpx text-grey"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view v-if="userStore.user" class="logout-section">
      <view class="logout-btn" @tap="handleLogout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();

// 快捷功能
const quickActions = ref([
  {
    title: "钱包",
    icon: "i-carbon-wallet text-32rpx",
    path: "/pages/wallet/index",
    bgColor: "var(--bg-primary-light)",
  },
  {
    title: "收藏",
    icon: "i-carbon-favorite text-32rpx",
    path: "/pages/user/favorites",
    bgColor: "var(--bg-danger-light)",
  },
  {
    title: "浏览历史",
    icon: "i-carbon-time text-32rpx",
    path: "/pages/user/history",
    bgColor: "var(--bg-info-light)",
  },
  {
    title: "优惠券",
    icon: "i-carbon-ticket text-32rpx",
    path: "/pages/user/coupons",
    bgColor: "var(--bg-warning-light)",
  },
]);

// 定义菜单项类型
interface MenuItem {
  title: string;
  icon: string;
  path: string;
  needLogin?: boolean;
  badge?: string | number;
}

// 菜单组
const menuGroups = ref<MenuItem[][]>([
  [
    {
      title: "我的简历",
      icon: "i-carbon-document text-32rpx text-primary",
      path: "/pages/job/resume/resume",
      needLogin: true,
    },
    {
      title: "我的职位",
      icon: "i-carbon-building text-32rpx text-blue",
      path: "/pages/job/manage",
      needLogin: true,
    },
    {
      title: "我的房源",
      icon: "i-carbon-home text-32rpx text-green",
      path: "/pages/house/manage",
      needLogin: true,
    },
  ],
  [
    {
      title: "客服中心",
      icon: "i-carbon-chat text-32rpx text-secondary",
      path: "/pages/service/customer",
    },
    {
      title: "意见反馈",
      icon: "i-carbon-edit text-32rpx text-secondary",
      path: "/pages/service/feedback",
    },
    {
      title: "帮助中心",
      icon: "i-carbon-help text-32rpx text-secondary",
      path: "/pages/service/help",
    },
  ],
  [
    {
      title: "关于我们",
      icon: "i-carbon-information text-32rpx text-secondary",
      path: "/pages/about/index",
    },
    {
      title: "隐私政策",
      icon: "i-carbon-security text-32rpx text-secondary",
      path: "/pages/about/privacy",
    },
  ],
]);

// 获取默认头像
const getDefaultAvatar = () => {
  return "/static/images/default-avatar.png";
};

// 处理登录
const handleLogin = () => {
  uni.navigateTo({
    url: "/pages/auth/login",
  });
};

// 处理头像点击
const handleAvatarTap = () => {
  uni.showActionSheet({
    itemList: ["查看头像", "更换头像"],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 查看头像
        uni.previewImage({
          urls: [userStore.user?.avatar || getDefaultAvatar()],
        });
      } else if (res.tapIndex === 1) {
        // 更换头像
        chooseAvatar();
      }
    },
  });
};

// 选择头像
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      // 这里可以添加头像上传逻辑
      console.log("选择的头像:", res.tempFilePaths[0]);
      uni.showToast({
        title: "头像上传功能待完善",
        icon: "none",
      });
    },
  });
};

// 跳转页面
const goToPage = (path: string) => {
  if (path) {
    uni.navigateTo({
      url: path,
    });
  }
};

// 跳转设置
const goToSettings = () => {
  uni.navigateTo({
    url: "/pages/settings/index",
  });
};

// 处理菜单点击
const handleMenuClick = (item: MenuItem) => {
  if (item.needLogin && !userStore.user) {
    uni.showModal({
      title: "提示",
      content: "请先登录",
      confirmText: "去登录",
      success: (res) => {
        if (res.confirm) {
          handleLogin();
        }
      },
    });
    return;
  }

  if (item.path) {
    goToPage(item.path);
  }
};

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: "确认退出",
    content: "确定要退出登录吗？",
    success: (res) => {
      if (res.confirm) {
        userStore.clearUserInfo();
        uni.showToast({
          title: "已退出登录",
          icon: "success",
        });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: var(--bg-page);
}

.nav-right {
  padding-right: var(--spacing-20);
}

/* 用户信息区域 */
.user-section {
  position: relative;
  padding: 0 var(--spacing-20) var(--spacing-32);
  margin-top: 100rpx;
}

.user-bg {
  position: absolute;
  top: -50rpx;
  left: 0;
  right: 0;
  height: 400rpx;
  overflow: hidden;
  border-radius: 0 0 var(--radius-xxl) var(--radius-xxl);
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    var(--primary-100) 0%,
    var(--primary-200) 100%
  );
}

.bg-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -50rpx;
  right: -50rpx;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: -30rpx;
  left: -30rpx;
}

.user-card {
  position: relative;
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--spacing-32);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  margin-top: 80rpx;
}

/* 未登录状态 */
.login-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-20) 0;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: var(--bg-tag);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-16);
}

.login-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
  margin-bottom: var(--spacing-8);
}

.login-desc {
  font-size: var(--font-size-sm);
  color: var(--text-info);
}

/* 已登录状态 */
.user-info-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-avatar-wrapper {
  position: relative;
  margin-bottom: var(--spacing-20);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.9);
}

.avatar-edit-icon {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid var(--bg-card);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.user-name-section {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-8);
}

.user-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-right: var(--spacing-12);
}

.vip-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-sm);
}

.vip-text {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: #8b5a00;
  margin-left: var(--spacing-4);
}

.user-intro {
  font-size: var(--font-size-sm);
  color: var(--text-info);
  text-align: center;
  margin-bottom: var(--spacing-20);
  line-height: var(--line-height-normal);
}

.user-stats {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-4);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-info);
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background: var(--border-color);
  margin: 0 var(--spacing-20);
}

/* 快捷功能区域 */
.quick-actions {
  margin: var(--spacing-20);
}

.action-grid {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-24);
  display: flex;
  justify-content: space-between;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.action-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-12);
}

.action-icon {
  color: var(--primary);
}

.action-text {
  font-size: var(--font-size-sm);
  color: var(--text-base);
  text-align: center;
}

/* 功能菜单区域 */
.menu-section {
  padding: 0 var(--spacing-20);
}

.menu-group {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-16);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-20) var(--spacing-24);
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: var(--bg-tag);
  }
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-16);
}

.menu-icon {
  font-size: var(--font-size-xl);
}

.menu-title {
  font-size: var(--font-size-base);
  color: var(--text-base);
  font-weight: var(--font-weight-medium);
}

.menu-right {
  display: flex;
  align-items: center;
}

.menu-badge {
  background: var(--text-red);
  color: var(--text-inverse);
  font-size: var(--font-size-xs);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-sm);
  margin-right: var(--spacing-12);
  min-width: 32rpx;
  text-align: center;
}

/* 退出登录按钮 */
.logout-section {
  padding: var(--spacing-32) var(--spacing-20);
}

.logout-btn {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-20);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:active {
    background: var(--bg-danger-light);
  }
}

.logout-text {
  font-size: var(--font-size-base);
  color: var(--text-red);
  font-weight: var(--font-weight-medium);
}

.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}
</style>
