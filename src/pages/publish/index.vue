<template>
  <view class="publish-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
      title="发布"
      left-icon="back"
      @clickLeft="goBack"
    />

    <view class="content">
      <view class="header-section">
        <text class="page-title">选择发布类型</text>
        <text class="page-subtitle">轻松发布，快速推广</text>
      </view>

      <!-- 房产分类 -->
      <view class="category-card">
        <view class="category-header">
          <view class="category-icon house-icon">
            <text class="i-carbon-home text-48rpx"></text>
          </view>
          <view class="category-info">
            <text class="category-title">房产</text>
            <text class="category-desc">发布房屋租售信息</text>
          </view>
        </view>
        
        <view class="publish-grid">
          <view 
            v-for="item in houseTypes" 
            :key="item.type"
            class="publish-item"
            @tap="navigateToPublish(item.path)"
          >
            <view class="item-icon">
              <text :class="item.icon" class="text-40rpx"></text>
            </view>
            <text class="item-name">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 招聘分类 -->
      <view class="category-card">
        <view class="category-header">
          <view class="category-icon job-icon">
            <text class="i-carbon-user-multiple text-48rpx"></text>
          </view>
          <view class="category-info">
            <text class="category-title">招聘</text>
            <text class="category-desc">发布职位信息</text>
          </view>
        </view>
        
        <view class="publish-grid">
          <view 
            class="publish-item"
            @tap="navigateToPublish('/pages/job/publish')"
          >
            <view class="item-icon">
              <text class="i-carbon-user-profile text-40rpx"></text>
            </view>
            <text class="item-name">发布职位</text>
          </view>
        </view>
      </view>

      <!-- 本地服务分类 -->
      <view class="category-card">
        <view class="category-header">
          <view class="category-icon service-icon">
            <text class="i-carbon-tools text-48rpx"></text>
          </view>
          <view class="category-info">
            <text class="category-title">本地服务</text>
            <text class="category-desc">发布各类服务信息</text>
          </view>
        </view>
        
        <view class="publish-grid">
          <view 
            v-for="item in serviceTypes" 
            :key="item.type"
            class="publish-item"
            @tap="navigateToPublish(item.path)"
          >
            <view class="item-icon">
              <text :class="item.icon" class="text-40rpx"></text>
            </view>
            <text class="item-name">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 房产发布类型
const houseTypes = [
  {
    type: 'rent',
    name: '租房',
    icon: 'i-carbon-home',
    path: '/pages/house/publish/rent'
  },
  {
    type: 'second-hand',
    name: '二手房',
    icon: 'i-carbon-building',
    path: '/pages/house/publish/second-hand'
  },
  {
    type: 'new-house',
    name: '新房',
    icon: 'i-carbon-apartment',
    path: '/pages/house/publish/new-house'
  },
  {
    type: 'commercial',
    name: '商铺/写字楼',
    icon: 'i-carbon-building-insights',
    path: '/pages/house/publish/commercial'
  }
];

// 本地服务发布类型
const serviceTypes = [
  {
    type: 'repair',
    name: '维修服务',
    icon: 'i-carbon-tools',
    path: '/pages/service/publish/repair'
  },
  {
    type: 'clean',
    name: '保洁服务', 
    icon: 'i-carbon-clean',
    path: '/pages/service/publish/clean'
  },
  {
    type: 'decoration',
    name: '装修服务',
    icon: 'i-carbon-paint-brush',
    path: '/pages/service/publish/decoration'
  },
  {
    type: 'transfer',
    name: '生意转让',
    icon: 'i-carbon-partnership',
    path: '/pages/service/publish/transfer'
  }
];

// 导航到发布页面
const navigateToPublish = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: () => {
      uni.showToast({
        title: '该功能开发中',
        icon: 'none'
      });
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.publish-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9ff 0%, #ffffff 100%);
}

.content {
  padding: 200rpx 32rpx 40rpx;
}

.header-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  display: block;
  margin-bottom: 16rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.category-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  }
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  color: white;
}

.house-icon {
  background: linear-gradient(135deg, #ff6b6b, #ff9a9e);
}

.job-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.service-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.category-info {
  flex: 1;
}

.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}

.category-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.publish-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.publish-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background: #fafbfc;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &:active {
    transform: scale(0.95);
    background: #f0f2f5;
  }
}

.item-icon {
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(135deg, #ff6d00, #ff9500);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  color: white;
}

.item-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}
</style> 